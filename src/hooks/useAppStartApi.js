import { useState, useCallback } from "react";
import { API_ENDPOINT } from "../utils/consts";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the app start API call
 * This API is called when a user starts the application form
 * @returns {Object} API state and methods
 */
export function useAppStartApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Notify the backend that the application has been started
   * @param {string} uuid - Application ID that was started
   * @returns {Promise<Object>} API response
   */
  const startApp = useCallback(async (uuid) => {
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/start`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message =
        error?.error ||
        error?.message ||
        "There was a problem starting the application. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    startApp,
    reset,
  };
}

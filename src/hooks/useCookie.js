import { useCallback, useState } from "react";
import { useRef } from "react";
import { logger } from "../utils/logger";
import { getRootDomain } from "../utils/cookieHelpers";

/**
 * Custom hook to manage cookie storage with a similar interface to useLocalStorage
 * @param {string} key - Cookie name
 * @param {any} initialValue - Default value if cookie doesn't exist
 * @param {Object} options - Cookie options
 * @param {number} options.days - Cookie expiration in days (default: 7)
 * @param {string} options.path - Cookie path (default: '/')
 * @param {string} options.domain - Cookie domain (default: root domain for subdomain sharing)
 * @param {boolean} options.secure - Whether cookie should only be sent over HTTPS (default: true in production)
 * @param {boolean} options.sameSite - SameSite attribute (default: 'Lax')
 * @returns {[any, Function, Function]} - [storedValue, setValue, removeItem]
 */
export function useCookie(key, initialValue, options = {}) {
  const {
    days = 7,
    path = "/",
    domain = getRootDomain(),
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;

  const initRef = useRef(false);

  // Helper function to get a cookie by name
  const getCookie = useCallback((name) => {
    const cookies = document.cookie.split("; ");
    const cookie = cookies.find((c) => c.startsWith(`${name}=`));

    if (!cookie) return null;

    const cookieValue = cookie.split("=")[1];

    try {
      // Try to parse as JSON
      return JSON.parse(decodeURIComponent(cookieValue));
    } catch {
      // If parsing fails, return as string
      return decodeURIComponent(cookieValue);
    }
  }, []);

  // Helper function to set a cookie
  const setCookie = useCallback((name, value, cookieOptions = {}) => {
    const {
      days = 7,
      path = "/",
      domain = getRootDomain(),
      secure = window.location.protocol === "https:",
      sameSite = "Lax",
    } = cookieOptions;

    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

    let cookieValue;

    if (typeof value === "string") {
      cookieValue = encodeURIComponent(value);
    } else {
      cookieValue = encodeURIComponent(JSON.stringify(value));
    }

    document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
      domain ? `; Domain=${domain}` : ""
    }${secure ? "; Secure" : ""}; SameSite=${sameSite}`;
  }, []);

  // Helper function to remove a cookie
  const removeCookie = useCallback((name, cookieOptions = {}) => {
    const { path = "/", domain = getRootDomain() } = cookieOptions;
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${
      domain ? `; Domain=${domain}` : ""
    }`;
  }, []);

  // Initialize state with value from cookie or initialValue
  const [storedValue, setStoredValue] = useState(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const cookieValue = getCookie(key);
      return cookieValue !== null ? cookieValue : initialValue;
    } catch (error) {
      logger.error(`Error reading cookie "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value, cookieOptions = {}) => {
      try {
        // Handle function updates
        const valueToStore =
          value instanceof Function ? value(storedValue) : value;

        if (valueToStore === null || typeof valueToStore === "undefined") {
          removeCookie(key, { path, domain });
          setStoredValue(null);
          return;
        } else {
          setStoredValue(valueToStore);
          setCookie(key, valueToStore, {
            days,
            path,
            domain,
            secure,
            sameSite,
            ...cookieOptions,
          });
        }
      } catch (error) {
        logger.error(`Error storing cookie "${key}":`, error);
      }
    },
    [
      storedValue,
      removeCookie,
      key,
      path,
      domain,
      setCookie,
      days,
      secure,
      sameSite,
    ]
  );

  // Function to remove the cookie
  const removeItem = useCallback(() => {
    try {
      removeCookie(key, { path, domain });
      setStoredValue(initialValue);
    } catch (error) {
      logger.error(`Error removing cookie "${key}":`, error);
    }
  }, [key, initialValue, removeCookie, path, domain]);

  // Initialize cookie with the current value on first render if it doesn't exist
  if (!initRef.current) {
    initRef.current = true;
    if (getCookie(key) === null) {
      setCookie(key, initialValue, { days, path, domain, secure, sameSite });
    }
  }

  return [storedValue, setValue, removeItem];
}

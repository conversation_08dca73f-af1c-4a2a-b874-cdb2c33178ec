import { useState, useCallback } from "react";
import { API_ENDPOINT } from "../utils/consts";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to validate a rep parameter
 * @returns {Object} API state and methods
 */
export function useValidateRepApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Validate a rep parameter
   * @param {string} rep - Rep parameter to validate
   * @returns {Promise<Object>} API response
   */
  const validateRep = useCallback(async (rep) => {
    let error_message = "";

    if (!rep) {
      error_message = "No rep parameter provided";
      setError({
        message: error_message,
        id: "MISSING_REP_PARAM",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/reps/${rep}/validate`);
      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message =
        error?.error ||
        error?.message ||
        "Failed to validate rep parameter. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    validateRep,
    reset,
  };
}

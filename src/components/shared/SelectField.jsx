import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";

/**
 * Select field component that integrates with react-hook-form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @param {Array} props.options - Select options array
 * @returns {JSX.Element}
 */
export const SelectField = ({
  name,
  label,
  control,
  rules = {},
  options = [],
  placeholder = "Select an option",
  ...rest
}) => {
  const {
    field: { onChange, value, ref },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  // Local state to track the select value
  const [selectValue, setSelectValue] = useState(value || "");

  // Update local state when the form value changes
  useEffect(() => {
    setSelectValue(value || "");
  }, [value]);

  /**
   * Handle select change - only updates local state
   * @param {React.ChangeEvent<HTMLSelectElement>} e - Change event
   */
  const handleChange = (e) => {
    setSelectValue(e.target.value);
  };

  // Create a unique ID for the select element
  const selectId = `${name}-select`;

  return (
    <div className="mb-6">
      <label
        htmlFor={selectId}
        className="block text-gray-700 text-base font-normal mb-2"
      >
        {label}
      </label>
      <select
        id={selectId}
        name={name}
        ref={ref}
        value={selectValue}
        onChange={handleChange}
        onBlur={() => {
          onChange(selectValue);
        }}
        className={`
          w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
          ${
            error
              ? "border-red-500 focus:ring-red-200"
              : "border-gray-300 focus:ring-blue-200"
          }
        `}
        {...rest}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <p className="text-red-500 text-xs italic mt-1">{error.message}</p>
      )}
    </div>
  );
};

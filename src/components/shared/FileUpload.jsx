import React, { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { useController } from "react-hook-form";
import {
  fileToBase64,
  base64ToFile,
  isSerializedFile,
} from "../../utils/fileHelpers";
import { logger } from "../../utils/logger";

// Maximum file size (2MB)
const MAX_FILE_SIZE = 2 * 1024 * 1024;

/**
 * File upload component with drag and drop functionality
 * Files are converted to base64 strings for storage in cookies
 * and reconstructed on page reload
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @param {number} props.maxFiles - Maximum number of files allowed
 * @param {number} props.maxSize - Maximum file size in bytes
 * @returns {JSX.Element}
 */
export const FileUpload = ({
  name,
  label,
  control,
  rules = {},
  maxFiles = 3,
  maxSize = MAX_FILE_SIZE,
}) => {
  const [fileErrors, setFileErrors] = useState([]);

  const {
    field: { onChange, value = [] },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: [],
  });

  // Add a useEffect to handle file reconstruction on component mount
  useEffect(() => {
    // If we have files in the value that are in serialized format (with dataUrl),
    // we need to create preview URLs for them
    if (value && value.length > 0) {
      const filesWithPreviews = value.map((file) => {
        // If the file already has a preview, return it as is
        if (file.preview) return file;

        // If the file has a dataUrl, it's a serialized file
        if (isSerializedFile(file)) {
          try {
            // Create a File object from the dataUrl for display purposes
            const fileObj = base64ToFile(file);
            // Create a preview URL for the file
            return {
              ...file,
              preview: URL.createObjectURL(fileObj),
            };
          } catch (error) {
            logger.error("Error creating preview for file:", error);
            return file;
          }
        }

        return file;
      });

      // Only update if we've added previews
      if (JSON.stringify(filesWithPreviews) !== JSON.stringify(value)) {
        onChange(filesWithPreviews);
      }
    }

    // Cleanup function to revoke object URLs when component unmounts
    return () => {
      if (value) {
        value.forEach((file) => {
          if (file.preview && typeof file.preview === "string") {
            URL.revokeObjectURL(file.preview);
          }
        });
      }
    };
  }, [value, onChange]); // Run when value or onChange changes

  const onDrop = useCallback(
    async (acceptedFiles, rejectedFiles) => {
      // Handle rejected files
      const errors = rejectedFiles
        .map(({ file, errors }) => {
          return errors.map((e) => {
            if (e.code === "file-too-large") {
              return `${file.name} is too large. Max size is ${
                maxSize / (1024 * 1024)
              }MB.`;
            }
            if (e.code === "file-invalid-type") {
              return `${file.name} has an invalid file type. Only PDF files are accepted.`;
            }
            return e.message;
          });
        })
        .flat();

      setFileErrors(errors);

      // If we already have maxFiles, don't add more
      if (value.length >= maxFiles) {
        setFileErrors((prev) => [
          ...prev,
          `Maximum of ${maxFiles} files allowed.`,
        ]);
        return;
      }

      // Add new files to the existing ones, up to maxFiles
      const newFiles = [...value];
      const remainingSlots = maxFiles - newFiles.length;

      // Process each file and convert to serializable format
      const processedFiles = await Promise.all(
        acceptedFiles.slice(0, remainingSlots).map(async (file) => {
          try {
            // Convert file to base64
            const dataUrl = await fileToBase64(file);

            // Create a serializable file object
            return {
              name: file.name,
              type: file.type,
              size: file.size,
              dataUrl,
              // Create a preview URL for display
              preview: URL.createObjectURL(file),
              // Add a timestamp to help with tracking changes
              lastModified: file.lastModified || Date.now(),
            };
          } catch (error) {
            logger.error("Error converting file to base64:", error);
            setFileErrors((prev) => [
              ...prev,
              `Error processing ${file.name}. Please try again.`,
            ]);
            return null;
          }
        })
      );

      // Filter out any failed conversions
      const validFiles = processedFiles.filter((file) => file !== null);

      // Add the new files to the existing ones
      newFiles.push(...validFiles);

      onChange(newFiles);
    },
    [value, onChange, maxFiles, maxSize]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    maxSize,
    maxFiles,
  });

  const removeFile = (index) => {
    const newFiles = [...value];

    // Revoke the preview URL to avoid memory leaks
    if (
      newFiles[index].preview &&
      typeof newFiles[index].preview === "string"
    ) {
      URL.revokeObjectURL(newFiles[index].preview);
    }

    newFiles.splice(index, 1);
    onChange(newFiles);
  };

  return (
    <div className="mb-6">
      <label className="block text-gray-700 text-base font-normal mb-2" data-hj-allow>
        {label}
      </label>

      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-sm p-6 text-center cursor-pointer
          ${
            isDragActive
              ? "border-blue-500 bg-blue-50"
              : "border-gray-300 hover:border-blue-500"
          }
          ${error ? "border-red-500 bg-red-50" : ""}
        `}
      >
        <input {...getInputProps()} />

        <div className="space-y-2">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>

          <div className="text-sm text-gray-600">
            <p className="font-medium">
              {isDragActive
                ? "Drop the files here"
                : "Drag and drop files here, or click to select files"}
            </p>
            <p className="mt-1" data-hj-allow>
              PDF files only (max {maxSize / (1024 * 1024)}MB per file)
            </p>
            <p className="mt-1" data-hj-allow>Upload {maxFiles} latest bank statements</p>
          </div>
        </div>
      </div>

      {/* File errors */}
      {fileErrors.length > 0 && (
        <div className="mt-2">
          {fileErrors.map((err, index) => (
            <p key={index} className="text-red-500 text-xs italic">
              {err}
            </p>
          ))}
        </div>
      )}

      {/* Form validation error */}
      {error && (
        <p className="text-red-500 text-xs italic mt-1">{error.message}</p>
      )}

      {/* File list */}
      {value.length > 0 && (
        <div className="mt-4 space-y-2">
          <p className="text-sm font-medium text-gray-700">Uploaded files:</p>
          <ul className="divide-y divide-gray-200 border border-gray-200 rounded-sm">
            {value.map((file, index) => (
              <li
                key={index}
                className="flex items-center justify-between py-3 px-4 hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <svg
                    className="h-5 w-5 text-gray-400 mr-3"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm text-gray-700 truncate max-w-xs" data-hj-suppress>
                    {file.name}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 text-sm font-medium"
                >
                  Remove
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

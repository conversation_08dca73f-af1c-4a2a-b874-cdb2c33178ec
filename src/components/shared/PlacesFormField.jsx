import React, { useEffect, useRef, useState, useCallback } from "react";
import { useController, useFormContext } from "react-hook-form";
import { Loader } from "@googlemaps/js-api-loader";
import { GOOGLE_MAPS_API_KEY } from "../../utils/consts";
import { logger } from "../../utils/logger";
import googleMapsLogo from "../../assets/GoogleMaps_Logo.svg";

/**
 * Places autocomplete field component that integrates with react-hook-form
 * Uses Google Maps Places Data API directly for more control
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @returns {JSX.Element}
 */
export const PlacesFormField = ({
  name,
  label,
  control,
  setAddressComponents = () => {},
  rules = {},
  placeholder = "",
  ...rest
}) => {
  const formMethods = useFormContext();
  const inputRef = useRef(null);
  const [inputValue, setInputValue] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [sessionToken, setSessionToken] = useState(null);
  const suggestionsRef = useRef(null);
  const debounceTimerRef = useRef(null);

  const {
    field: { onChange, value, ref },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: "",
  });

  // Process address components from a place object
  const processAddressComponents = async (place) => {
    try {
      // Fetch the necessary fields
      await place.fetchFields({
        fields: ["addressComponents", "formattedAddress"],
      });

      const addressComponents = place.addressComponents;
      if (!addressComponents) return;

      let addressLine1 = "";
      let streetNumber = "";
      let route = "";
      let city = "";
      let state = "";
      let zipCode = "";

      // Process address components
      addressComponents.forEach((component) => {
        const types = component.types;

        if (types.includes("street_number")) {
          streetNumber = component.longText;
        } else if (types.includes("route")) {
          route = component.longText;
        } else if (types.includes("locality")) {
          city = component.longText;
        } else if (types.includes("sublocality")) {
          city = component.shortText;
        } else if (types.includes("sublocality_level_1")) {
          city = component.shortText;
        } else if (types.includes("administrative_area_level_1")) {
          state = component.shortText;
        } else if (types.includes("postal_code")) {
          zipCode = component.longText;
        }
      });

      // Combine street number and route with a space between
      addressLine1 = [streetNumber, route].filter(Boolean).join(" ");

      // If we couldn't extract a proper address line, use the formatted address
      if (!addressLine1 && place.formattedAddress) {
        // Use the first line of the formatted address
        addressLine1 = place.formattedAddress.split(",")[0];
      }

      formMethods.setValue(name, addressLine1);
      setInputValue(addressLine1);
      setAddressComponents({ addressLine1, city, state, zipCode });

      // Reset suggestions
      setSuggestions([]);
      setShowSuggestions(false);

      // Create a new session token for the next search
      createNewSessionToken();
    } catch (error) {
      logger.error("Error processing place:", error);
    }
  };

  // Create a new session token
  const createNewSessionToken = useCallback(async () => {
    try {
      if (!window.google) return;

      const { AutocompleteSessionToken } =
        await window.google.maps.importLibrary("places");
      const token = new AutocompleteSessionToken();
      setSessionToken(token);
    } catch (error) {
      logger.error("Error creating session token:", error);
    }
  }, []);

  // Fetch autocomplete suggestions
  const fetchSuggestions = useCallback(
    async (input) => {
      if (!input || input.length < 2) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      try {
        setIsLoading(true);

        const { AutocompleteSuggestion } =
          await window.google.maps.importLibrary("places");

        // Create request object
        const request = {
          input,
          sessionToken,
          includedRegionCodes: ["us"],
          language: "en-US",
          region: "us",
        };

        // Fetch suggestions
        const { suggestions } =
          await AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

        setSuggestions(suggestions);
        setShowSuggestions(true);
        setSelectedIndex(-1);
      } catch (error) {
        logger.error("Error fetching suggestions:", error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    },
    [sessionToken]
  );

  // Handle input change with debounce
  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    onChange(value);

    // Clear previous timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set new timer
    debounceTimerRef.current = setTimeout(() => {
      fetchSuggestions(value);
    }, 300);
  };

  // Handle suggestion selection
  const handleSuggestionClick = async (suggestion) => {
    try {
      const place = suggestion.placePrediction.toPlace();
      await processAddressComponents(place);
    } catch (error) {
      logger.error("Error selecting suggestion:", error);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) return;

    // Arrow down
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prevIndex) =>
        prevIndex < suggestions.length - 1 ? prevIndex + 1 : prevIndex
      );
    }

    // Arrow up
    else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0));
    }

    // Enter
    else if (e.key === "Enter" && selectedIndex >= 0) {
      e.preventDefault();
      handleSuggestionClick(suggestions[selectedIndex]);
    }

    // Escape
    else if (e.key === "Escape") {
      setShowSuggestions(false);
    }
  };

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(e.target) &&
        inputRef.current !== e.target
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Initialize Google Maps Places API
  useEffect(() => {
    const loader = new Loader({
      apiKey: GOOGLE_MAPS_API_KEY,
      version: "weekly",
      libraries: ["places"],
    });

    const loadGoogleMaps = () => {
      try {
        // Load the Google Maps API with places library
        loader.importLibrary("places").then(() => {
          // Access the global google object
          if (!window.google) {
            logger.error("Google Maps API failed to load");
            return;
          }

          // Create a new session token
          createNewSessionToken();
        });
      } catch (error) {
        logger.error("Error loading Google Maps API:", error);
      }
    };

    loadGoogleMaps();

    // Cleanup function
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [createNewSessionToken]);

  // Update input value when form value changes externally
  useEffect(() => {
    if (value && value !== inputValue) {
      setInputValue(value);
    }
  }, [value, inputValue]);

  return (
    <div className="mb-6 relative">
      <label
        htmlFor={name}
        className="block text-gray-700 text-base font-normal mb-2"
      >
        {label}
      </label>
      <input
        data-hj-suppress
        ref={(e) => {
          inputRef.current = e;
          ref(e);
        }}
        id={name}
        name={name}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => inputValue && fetchSuggestions(inputValue)}
        placeholder={placeholder}
        autoComplete="off"
        className={`
          w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
          ${
            error
              ? "border-red-500 focus:ring-red-200"
              : "border-gray-300 focus:ring-blue-200"
          }
        `}
        {...rest}
      />

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-sm shadow-lg max-h-60 overflow-auto"
          data-hj-suppress
        >
          <ul>
            {suggestions.map((suggestion, index) => (
              <li
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${
                  index === selectedIndex ? "bg-gray-100" : ""
                }`}
              >
                {suggestion.placePrediction.text.toString()}
              </li>
            ))}
          </ul>
          {/* Google Maps logo attribution with info icon */}
          <div className="flex items-center justify-end p-2 border-t border-gray-200">
            <img
              src={googleMapsLogo}
              alt="Powered by Google Maps"
              className="h-5 object-contain"
            />
            <a
              href="https://www.google.com/help/terms_maps/"
              target="_blank"
              rel="noopener noreferrer"
              className="ml-2 text-gray-500 hover:text-gray-700"
              aria-label="Google Maps Terms of Service"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-5 h-5"
              >
                <path
                  fillRule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z"
                  clipRule="evenodd"
                />
              </svg>
            </a>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute right-3 top-10">
          <svg
            className="animate-spin h-5 w-5 text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      )}

      {error && (
        <p className="text-red-500 text-xs italic mt-1">{error.message}</p>
      )}
    </div>
  );
};

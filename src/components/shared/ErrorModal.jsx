import React, { useEffect } from "react";
import { createPortal } from "react-dom";

/**
 * Error modal component
 * @param {Object} props - Component props
 * @param {string} props.error - Error message to display
 * @param {string} props.errorId - Error ID from the API
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @returns {JSX.Element|null}
 */
export const ErrorModal = ({
  error = "Something went wrong!",
  errorId,
  isOpen,
  onClose,
}) => {
  useEffect(() => {
    const initialOverflow = document.body.style.overflow;
    if (isOpen) document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = initialOverflow || "auto";
    };
  }, [isOpen]);

  if (!isOpen) return null;
  return (
    <Portal>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/10 ">
        <div className="bg-white rounded-sm shadow-xl max-w-md w-full p-6 relative">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-red-600">Error</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-sm p-4">
              <div className="flex items-start">
                <div className="bg-red-100 rounded-full p-2 mr-3 flex-shrink-0 mt-1">
                  <svg
                    className="h-5 w-5 text-red-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <div>
                  <p className="text-gray-800">{error}</p>
                  {errorId && (
                    <p className="text-sm text-gray-500 mt-2">
                      Error ID: <span className="font-mono">{errorId}</span>
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </Portal>
  );
};

function Portal({ children }) {
  return createPortal(children, document.body);
}

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { formatSSN, parseSSN, maskSSN } from "../../utils/formatters";

/**
 * SSN field component that integrates with react-hook-form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @returns {JSX.Element}
 */
export const SSNField = ({
  name,
  label,
  control,
  rules = {},
  placeholder = "XXX-XX-XXXX",
  ...rest
}) => {
  const {
    field: { onChange, value, ref, onBlur: fieldOnBlur },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: "",
  });

  // State to track if the field is focused
  const [isFocused, setIsFocused] = useState(false);

  // Format the display value - show masked value when not focused
  const displayValue = value
    ? isFocused
      ? formatSSN(value)
      : maskSSN(value)
    : "";

  /**
   * Handles input change events
   * @param {React.ChangeEvent<HTMLInputElement>} e - Change event
   */
  const handleChange = (e) => {
    const inputValue = e.target.value;
    // Extract numeric value from formatted string
    const numericValue = parseSSN(inputValue);
    // Update the form value with the numeric value
    onChange(numericValue);
  };

  /**
   * Handles focus events
   */
  const handleFocus = () => {
    setIsFocused(true);
  };

  /**
   * Handles blur events
   */
  const handleBlur = () => {
    setIsFocused(false);
    // Call the original onBlur from react-hook-form
    if (fieldOnBlur) {
      fieldOnBlur();
    }
  };

  return (
    <div className="mb-6">
      <label
        htmlFor={name}
        className="block text-gray-700 text-base font-base mb-2"
      >
        {label}
      </label>
      <input
        data-hj-suppress
        type="text"
        ref={ref}
        value={displayValue}
        name={name}
        id={name}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        inputMode="numeric"
        maxLength="11"
        className={`
          w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
          ${
            error
              ? "border-red-500 focus:ring-red-200"
              : "border-gray-300 focus:ring-blue-200"
          }
        `}
        {...rest}
      />
      {error && (
        <p className="text-red-500 text-xs italic mt-1">{error.message}</p>
      )}
    </div>
  );
};

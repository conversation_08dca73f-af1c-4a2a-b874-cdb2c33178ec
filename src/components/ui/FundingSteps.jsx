import { trackClick } from "../../utils/analytics";
import fundingStepsImg from "../../assets/funding_steps_img.webp";
import fundingStepsImgMd from "../../assets/funding_steps_img_md.webp";
import fundingStepsImgSm from "../../assets/funding_steps_img_sm.webp";

const FundingSteps = () => {
  const handleApplyNowClick = () => {
    trackClick("How It Works - Apply Now");
    // Scroll to the form section
    const formElement = document.getElementById(
      "prequalify-form-scroll-target"
    );
    if (formElement) {
      formElement.scrollIntoView({ behavior: "smooth" });
    }
  };

  const steps = [
    {
      title: "Apply Online",
      description:
        "Fill out our quick, 5-minute application—no paperwork, no hassle.",
    },
    {
      title: "Review & Decide",
      description:
        "Your dedicated funding specialist walks you through your offers and helps you choose what’s right.",
    },
    {
      title: "Get Funded",
      description:
        "Receive capital in as little as 24 hours—fast, simple, and secure.",
    },
    {
      title: "Grow Together",
      description:
        "Over 90% of qualified clients return for additional funding. We’re here for the long haul.",
    },
  ];

  return (
    <div className="mt-20 mb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-gray-50 rounded-lg overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Left side - Content */}
            <div className="lg:w-1/2 p-8 lg:p-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-8">
                How It Works
              </h2>

              <div className="space-y-6">
                {steps.map((step, index) => (
                  <div key={index} className="flex flex-col">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">
                      {step.title}
                    </h3>
                    <p className="text-gray-700 text-sm sm:text-base leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                ))}
              </div>

              <button
                onClick={handleApplyNowClick}
                className="mt-8 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Get Started
              </button>
            </div>

            {/* Right side - Image */}
            <div className="lg:w-1/2 relative min-h-[400px] lg:min-h-[500px]">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200 lg:rounded-r-lg">
                {/* Placeholder for the business professionals image */}
                <picture>
                  <source
                    media="(max-width: 640px)"
                    srcSet={`${fundingStepsImgSm}`}
                  />
                  <source
                    media="(max-width: 1024px)"
                    srcSet={`${fundingStepsImgMd}`}
                  />
                  <img
                    src={fundingStepsImg}
                    srcSet={`${fundingStepsImg} 1x`}
                    alt=""
                    className="w-full h-full object-cover"
                    loading="lazy"
                    decoding="async"
                  />
                </picture>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundingSteps;

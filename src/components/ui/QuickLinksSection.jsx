import { trackClick } from "../../utils/analytics";

const QuickLinksSection = () => {
  const handlePhoneClick = () => {
    trackClick("Phone", { phone: "(*************" });
  };

  const handleFAQClick = () => {
    trackClick("FAQ", {
      destination: "https://pinnaclefundingco.com/faq/",
    });
  };

  const handleWebsiteClick = () => {
    trackClick("Website", {
      destination: "https://pinnacleconsultingny.com/",
    });
  };
  return (
    <div className="mt-12">
      <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-10 text-center">
        We’re Here to Help!
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Need personalized support?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Connect with a funding specialist
          </p>
          <a
            href="tel:3476948180"
            className="flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200 text-base sm:text-lg"
            onClick={handlePhoneClick}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
            (*************
          </a>
        </div>

        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Have more questions?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Explore our comprehensive{" "}
            <a
              href="https://pinnaclefundingco.com/faq/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200"
              onClick={handleFAQClick}
            >
              FAQ
            </a>{" "}
            for quick answers and helpful insights.
          </p>
          <div />
        </div>

        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Want to learn more?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Discover how we work and what we offer on our{" "}
            <a
              href="https://pinnacleconsultingny.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200"
              onClick={handleWebsiteClick}
            >
              Website
            </a>
            .
          </p>
          <div />
        </div>
      </div>
    </div>
  );
};

export default QuickLinksSection;

import { useState } from "react";
import { trackClick, trackCustomEvent } from "../../utils/analytics";

const FAQItem = ({ question, answer, isOpen, toggleOpen, isLast }) => {
  return (
    <div className={`py-4 ${!isLast ? "border-b border-gray-200" : ""}`}>
      <button className="flex w-full justify-between items-center text-left focus:outline-none group" onClick={toggleOpen}>
        <h3
          className={`text-base sm:text-lg font-medium ${isOpen ? "text-blue-700" : "text-gray-900"
            } group-hover:text-blue-700 transition-colors duration-200`}
        >
          {question}
        </h3>
        <span className="ml-6 flex-shrink-0">
          <svg
            className={`h-5 w-5 transform transition-transform duration-200 ${isOpen ? "rotate-180 text-blue-600" : "rotate-0 text-gray-500"
              } group-hover:text-blue-600`}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
          </svg>
        </span>
      </button>
      <div
        className={`mt-2 pr-12 overflow-hidden transition-all duration-300 ease-in-out ${isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          }`}
      >
        <div className="text-sm sm:text-base text-gray-700 pb-2">{answer}</div>
      </div>
    </div>
  );
};

const PrequalifyFAQ = () => {
  const [openIndex, setOpenIndex] = useState(-1); // Start with all closed

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  const handlePhoneClick = () => {
    trackClick("FAQ Phone", { phone: "(*************" });
  };

  const handleApplyNowClick = () => {
    trackCustomEvent("faq_apply_now_click", true);
    // Scroll to the form section
    const formElement = document.getElementById("prequalify-form-scroll-target");
    if (formElement) {
      formElement.scrollIntoView({ behavior: "smooth" });
    }
  };

  const faqItems = [
    {
      question: "What do I need to qualify?",
      answer: (
        <ul className="space-y-1.5">
          <li className="flex items-baseline">
            <span className="mr-1.5 flex-shrink-0">•</span>
            <span>Minimum six months in business.</span>
          </li>
          <li className="flex items-baseline">
            <span className="mr-1.5 flex-shrink-0">•</span>
            <span>Minimum of $180,000 in annual revenue</span>
          </li>
          <li className="flex items-baseline">
            <span className="mr-1.5 flex-shrink-0">•</span>
            <span>Minimum 525 Personal FICO score</span>
          </li>
          <li className="flex items-baseline">
            <span className="mr-1.5 flex-shrink-0">•</span>
            <span>Business checking account</span>
          </li>
        </ul>
      ),
    },
    {
      question: "What is the application process for Pinnacle Funding?",
      answer: (
        <p>
          We pride ourselves on our quick and simple application process. It takes under 5 minutes to fill out our online application. We
          will ask for basic information on you and your business to get you the best offer. You can also apply over the phone by calling
          (*************.
        </p>
      ),
    },
    {
      question: "How soon can I get my funds?",
      answer: (
        <p>
          Pinnacle offers fast business funding, with approved businesses typically able to access business funding in 24 hours of
          completing a full application.
        </p>
      ),
    },
    {
      question: "Will there be a hard credit pull?",
      answer: (
        <p>
          We believe in simple and stress-free funding with no hard credit pull. There are no fees, no collateral, and no credit impact when
          you apply.
        </p>
      ),
    },
  ];

  return (
    <div className="mt-16 mb-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-4">Frequently Asked Questions</h2>
        </div>

        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-sm p-4 py-2 sm:p-6 sm:py-3 divide-y divide-gray-200">
          {faqItems.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              toggleOpen={() => toggleFAQ(index)}
              isLast={index === faqItems.length - 1}
            />
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-base sm:text-lg text-gray-700 mb-4">Apply for Fast Business Loans</p>
          <button
            onClick={handleApplyNowClick}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Apply Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrequalifyFAQ;

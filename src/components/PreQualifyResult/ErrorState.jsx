import React from "react";
import { Link } from "react-router-dom";

/**
 * Error state component for the PreQualifyResult page
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {string} props.errorId - Error ID from the API
 * @returns {JSX.Element}
 */
export const ErrorState = ({ message, errorId }) => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-sm shadow-lg p-6 text-center">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <div className="bg-red-50 border border-red-200 rounded-sm p-4 mb-6">
            <p className="text-gray-800 mb-2">
              {message || "Application not found"}
            </p>
            {errorId && (
              <p className="text-sm text-gray-500">
                Error ID: <span className="font-mono">{errorId}</span>
              </p>
            )}
          </div>
          <Link
            to="/"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-sm focus:outline-none focus:shadow-outline inline-block"
          >
            Return to Pre-Qualification Form
          </Link>
        </div>
      </div>
    </div>
  );
};

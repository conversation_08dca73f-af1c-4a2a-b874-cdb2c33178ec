import { FormField } from "../../shared/FormField.jsx";
import { PhoneField } from "../../shared/PhoneField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { CheckboxField } from "../../shared/CheckboxField.jsx";
import { validationSchema } from "../../../utils/validationSchema";

/**
 * FICO score range options
 */
const ficoOptions = [
  { value: "780-850", label: "Excellent (780-850)" },
  { value: "700-780", label: "Very Good (700-780)" },
  { value: "625-700", label: "Good (625-700)" },
  { value: "550-625", label: "Fair (550-625)" },
  { value: "300-550", label: "Poor (300-550)" },
];

/**
 * Owner Information step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const OwnerInformation = ({ control }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">Let's get to know you</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-0">
        <FormField
          label="First Name"
          name="firstName"
          type="text"
          placeholder="John"
          control={control}
          rules={validationSchema.firstName}
          data-hj-allow
        />

        <FormField
          label="Last Name"
          name="lastName"
          type="text"
          placeholder="Smith"
          control={control}
          rules={validationSchema.lastName}
          data-hj-suppress
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-0">
        <FormField
          label="Email"
          name="email"
          type="email"
          inputMode="email"
          placeholder="<EMAIL>"
          control={control}
          rules={validationSchema.email}
          data-hj-suppress
        />

        <PhoneField
          name="phone"
          label="Phone"
          type="tel"
          inputMode="tel"
          control={control}
          rules={validationSchema.phone}
          data-hj-suppress
        />
      </div>

      <SelectField
        name="estimatedFICO"
        label="Estimated Credit Score"
        control={control}
        options={ficoOptions}
        placeholder="Select Credit Score Range..."
        rules={validationSchema.estimatedFICO}
        data-hj-allow
      />

      <CheckboxField
        name="consent"
        label={
          <>
            By submitting the form you are consenting to be contacted by phone,
            SMS, and email from Pinnacle Funding. Message & data rates may
            apply. You may OPT-OUT of communications at any time. See our{" "}
            <a
              href="https://pinnaclefundingco.com/privacy-policy/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline hover:text-blue-800 transition-colors"
            >
              Privacy Policy
            </a>{" "}
            and{" "}
            <a
              href="https://pinnaclefundingco.com/privacy-policy/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline hover:text-blue-800 transition-colors"
            >
              Terms
            </a>
            .
          </>
        }
        control={control}
        rules={validationSchema.consent}
        data-hj-allow
      />
    </div>
  );
};

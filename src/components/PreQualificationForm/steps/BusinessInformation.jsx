import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { FormField } from "../../shared/FormField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { CurrencyField } from "../../shared/CurrencyField.jsx";
import { validationSchema } from "../../../utils/validationSchema";

/**
 * Revenue range options
 */
const revenueOptions = [
  { value: "0-10000", label: "$0 - $10,000" },
  { value: "10000-25000", label: "$10,000 - $25,000" },
  { value: "************", label: "$25,000 - $100,000" },
  { value: "100000-250000", label: "$100,000 - $250,000" },
  { value: "250000-1000000", label: "$250,000 - $1,000,000" },
  { value: "1000000+", label: "$1,000,000+" },
];

/**
 * Month options for business start date
 */
const monthOptions = [
  { value: "01", label: "January" },
  { value: "02", label: "February" },
  { value: "03", label: "March" },
  { value: "04", label: "April" },
  { value: "05", label: "May" },
  { value: "06", label: "June" },
  { value: "07", label: "July" },
  { value: "08", label: "August" },
  { value: "09", label: "September" },
  { value: "10", label: "October" },
  { value: "11", label: "November" },
  { value: "12", label: "December" },
];

/**
 * Year options for business start date
 */
const generateYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let year = currentYear; year >= 1900; year--) {
    years.push({ value: year.toString(), label: year.toString() });
  }
  return years;
};

const yearOptions = generateYearOptions();

/**
 * Business Information step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const BusinessInformation = ({ control }) => {
  const {
    field: { value: monthlyRevenueValue, onChange: setMonthlyRevenue },
  } = useController({
    name: "monthlyRevenue",
    control,
  });

  const {
    field: { value: annualRevenueValue, onChange: setAnnualRevenue },
  } = useController({
    name: "annualRevenue",
    control,
  });

  // Check if annual revenue is 120k or greater
  const annualRevenueNumber = annualRevenueValue
    ? parseInt(annualRevenueValue, 10)
    : 0;
  const shouldUpgradeRevenue = annualRevenueNumber >= 120000;

  // Auto-upgrade monthly revenue selection when annual revenue is 120k+
  useEffect(() => {
    if (shouldUpgradeRevenue && monthlyRevenueValue === "0-10000") {
      setMonthlyRevenue("10000-25000");
      setAnnualRevenue(""); // Clear the annual revenue field
    }
  }, [
    shouldUpgradeRevenue,
    monthlyRevenueValue,
    setMonthlyRevenue,
    setAnnualRevenue,
  ]);

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">
        Tell us about your business
      </h3>

      <FormField
        label="Business Name"
        name="businessName"
        type="text"
        placeholder="Acme LLC"
        control={control}
        rules={validationSchema.businessName}
        data-hj-suppress
      />

      <SelectField
        name="monthlyRevenue"
        label="Monthly Business Revenue"
        control={control}
        options={revenueOptions}
        placeholder="Select monthly revenue..."
        rules={validationSchema.monthlyRevenue}
        data-hj-allow
      />

      {monthlyRevenueValue === "0-10000" && !shouldUpgradeRevenue && (
        <CurrencyField
          name="annualRevenue"
          label="Confirm your estimated total yearly sales"
          control={control}
          placeholder="$0"
          data-hj-allow
        />
      )}

      <BusinessStartDateInput control={control} />
    </div>
  );
};

const BusinessStartDateInput = ({ control }) => {
  const {
    field: { value: businessStartDate = "", onChange: setBusinessStartDate },
    fieldState: { error },
  } = useController({
    name: "businessStartDate",
    control,
    rules: validationSchema.businessStartDate,
  });

  // Local state for month and year
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");

  // Initialize month and year from businessStartDate if it exists
  useEffect(() => {
    if (businessStartDate) {
      const parts = businessStartDate.split("-");
      if (parts.length === 3) {
        // Format is YYYY-MM-DD
        setYear(parts[0]);
        setMonth(parts[1]);
      }
    }
  }, [businessStartDate]);

  // Update businessStartDate when month or year changes
  const updateBusinessStartDate = (newMonth, newYear) => {
    if (newMonth && newYear) {
      // Format as YYYY-MM-DD with "01" as the day
      setBusinessStartDate(`${newYear}-${newMonth}-01`);
    } else if (!newMonth || !newYear) {
      setBusinessStartDate("");
    }
  };

  // Handle month change
  const handleMonthChange = (e) => {
    const newMonth = e.target.value;
    setMonth(newMonth);
    updateBusinessStartDate(newMonth, year);
  };

  // Handle year change
  const handleYearChange = (e) => {
    const newYear = e.target.value;
    setYear(newYear);
    updateBusinessStartDate(month, newYear);
  };

  return (
    <div className="mb-4">
      <span className="block text-gray-700 text-base font-base mb-2">
        When did you start your business?
      </span>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="mb-4">
          <label
            htmlFor="businessStartMonth"
            className="block text-gray-700 text-sm font-base mb-2"
          >
            Month
          </label>
          <select
            id="businessStartMonth"
            name="businessStartMonth"
            value={month}
            onChange={handleMonthChange}
            data-hj-allow
            className={`
                w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
                ${
                  error
                    ? "border-red-500 focus:ring-red-200"
                    : "border-gray-300 focus:ring-blue-200"
                }
              `}
          >
            <option value="">Select Month</option>
            {monthOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-4">
          <label
            htmlFor="businessStartYear"
            className="block text-gray-700 text-sm font-base mb-2"
          >
            Year
          </label>
          <select
            id="businessStartYear"
            name="businessStartYear"
            value={year}
            onChange={handleYearChange}
            data-hj-allow
            className={`
                w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
                ${
                  error
                    ? "border-red-500 focus:ring-red-200"
                    : "border-gray-300 focus:ring-blue-200"
                }
              `}
          >
            <option value="">Select Year</option>
            {yearOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      {error && (
        <p className="text-red-500 text-xs italic mt-1">{error.message}</p>
      )}
    </div>
  );
};

import React from "react";
import { FileUpload } from "../../shared/FileUpload.jsx";
import { validationSchema } from "../../../utils/validationSchema";

/**
 * Bank Statements step component
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onSkip - Function to handle skipping this step
 * @returns {JSX.Element}
 */
export const BankStatements = ({ control, onSkip }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Upload Bank Statements</h3>

      <p className="text-gray-600 mb-6" data-hj-allow>
        Please upload your last 3 months of business bank statements.
      </p>

      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              For faster processing, please ensure:
            </p>
            <ul className="list-disc list-inside text-sm text-blue-700 mt-1" data-hj-allow>
              <li>Files are in PDF format</li>
              <li>Each file is less than 2MB</li>
              <li>Statements show your business name and account number</li>
              <li>Statements are for the most recent 3 months</li>
            </ul>
          </div>
        </div>
      </div>

      <FileUpload
        name="bankStatements"
        label="Bank Statements (PDF only, max 2MB each)"
        control={control}
        rules={validationSchema.bankStatements}
        acceptedFileTypes={["application/pdf"]}
        maxFiles={3}
        maxSize={2 * 1024 * 1024} // 2MB
      />

      <div className="mt-4 text-sm text-gray-500">
        <p>
          Your bank statements are securely stored and will only be used for
          underwriting purposes.
        </p>
        <button
          type="button"
          onClick={onSkip}
          className="text-blue-600 underline hover:text-blue-800 focus:outline-none mt-2"
        >
          Skip this step, I'll email them manually
        </button>
      </div>
    </div>
  );
};

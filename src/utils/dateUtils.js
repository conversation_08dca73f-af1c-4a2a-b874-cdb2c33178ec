/**
 * Generate month options for select dropdown
 * @returns {Array<{value: string, label: string}>} Array of month options
 */
export const generateMonthOptions = () => {
  const months = [
    { value: "01", label: "January" },
    { value: "02", label: "February" },
    { value: "03", label: "March" },
    { value: "04", label: "April" },
    { value: "05", label: "May" },
    { value: "06", label: "June" },
    { value: "07", label: "July" },
    { value: "08", label: "August" },
    { value: "09", label: "September" },
    { value: "10", label: "October" },
    { value: "11", label: "November" },
    { value: "12", label: "December" },
  ];
  return months;
};

/**
 * Generate year options for select dropdown
 * @param {number} startYear - Starting year (default: current year - 100)
 * @param {number} endYear - Ending year (default: current year)
 * @returns {Array<{value: string, label: string}>} Array of year options
 */
export const generateYearOptions = (
  startYear = new Date().getFullYear() - 100,
  endYear = new Date().getFullYear()
) => {
  const years = [];
  for (let year = endYear; year >= startYear; year--) {
    years.push({ value: year.toString(), label: year.toString() });
  }
  return years;
};

/**
 * Format date as MM/DD/YYYY
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString) => {
  if (!dateString) return "";

  const [year, month, day] = dateString.split("-");
  return `${month}/${day}/${year}`;
};

/**
 * Parse date from MM/DD/YYYY to YYYY-MM-DD
 * @param {string} dateString - Date string in MM/DD/YYYY format
 * @returns {string} Date string in YYYY-MM-DD format
 */
export const parseDate = (dateString) => {
  if (!dateString) return "";

  const [month, day, year] = dateString.split("/");
  return `${year}-${month}-${day}`;
};

/**
 * Calculate months in business from a business start date
 * @param {string} businessStartDate - Business start date in YYYY-MM-DD format
 * @returns {number} Number of months in business (rounded down to whole number)
 */
export const calculateMonthsInBusiness = (businessStartDate) => {
  if (!businessStartDate) return 0;

  const startDate = new Date(businessStartDate);
  const currentDate = new Date();

  // Calculate difference in months
  const months =
    (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
    (currentDate.getMonth() - startDate.getMonth());

  return Math.max(0, months);
};

/**
 * Calculate age in years from a date of birth
 * @param {string} dateOfBirth - Date of birth in YYYY-MM-DD format
 * @returns {number} Age in years (rounded down to whole number)
 */
export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) return 0;

  const birthDate = new Date(dateOfBirth);
  const currentDate = new Date();

  let age = currentDate.getFullYear() - birthDate.getFullYear();

  return Math.max(0, age);
};

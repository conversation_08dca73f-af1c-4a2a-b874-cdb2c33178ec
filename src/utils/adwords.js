import { logger } from "./logger";
import { collectUtmCookies } from "./cookieHelpers";

export const AdwordsConversions = {
  PREQUAL_APPROVED: "AW-16778466762/Bv7HCMSRkO0aEMqrzMA-",
  PREQUAL_DENIED: "AW-16778466762/qqEiCIn0ne0aEMqrzMA-",
};

const log = (...args) => {
  logger.log("[AdWords]: ", ...args);
};

export function trackConversion(key) {
  const utmParams = collectUtmCookies();
  const isGoogleAdwords = utmParams?.utm_source?.toLowerCase() === "googleadwords";

  if (!isGoogleAdwords) {
    return;
  }

  const conversionId = AdwordsConversions[key];
  if (!conversionId) {
    log(`Unknown Adwords conversion key: ${key}`);
    return;
  }

  if (window.gtag) {
    log("Tracking Conversion:", conversionId);
    window.gtag("event", "conversion", { send_to: conversionId });
  }
}

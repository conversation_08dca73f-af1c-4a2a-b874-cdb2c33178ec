// Storage keys
export const UTM_STORAGE_KEY = "utmParams";
export const PRE_QUALIFY_FORM_KEY = "preQualifyFields";
export const PRE_QUALIFY_RESULT_KEY = "preQualifyResult";
export const FAST_TRACK_FORM_KEY = "fastTrackFields";
export const FAST_TRACK_ACTIVE_KEY = "fastTrackActive";
export const APPLICATION_FORM_KEY = "applicationFields";
export const APPLICATION_RESULT_KEY = "applicationResult";
export const APPLICATION_ID_KEY = "applicationId";
export const APPLICATION_STARTED_KEY = "applicationStarted";

export const API_ENDPOINT = import.meta.env.VITE_API_ENDPOINT;
export const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
export const MAIN_DOMAIN = import.meta.env.VITE_MAIN_DOMAIN;
export const FT_DOMAIN = import.meta.env.VITE_FT_DOMAIN;
export const IS_DEV_MODE =
  import.meta.env.VITE_DEV_MODE === "true" ? true : false;

export const APP_FLOW_STATUS = {
  PREQUAL_DENIED: "PREQUAL_DENIED",
  PREQUAL_APPROVED: "PREQUAL_APPROVED",

  PREQUAL_FAST_TRACK: "PREQUAL_FAST_TRACK",
  APP_GENERATED: "APP_GENERATED",

  APP_STARTED: "APP_STARTED",
  APP_SUBMITTED: "APP_SUBMITTED",
  APP_EDITING: "APP_EDITING",
  APP_SIGNED: "APP_SIGNED",
  APP_COMPLETED: "APP_COMPLETED",
};

export const defaultPreQualifyValues = {
  fundingAmount: "",
  purpose: "",
  topPriority: "",
  timeline: "",
  businessName: "",
  monthlyRevenue: "",
  annualRevenue: "",
  businessStartDate: "",
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  estimatedFICO: "",
  consent: true,
  currentStep: 0,
};

export const defaultFastTrackValues = {
  businessName: "",
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  estimatedFICO: "",
  consent: true,
};

export const defaultApplicationValues = {
  // Business Info
  businessName: "",
  dbaName: "",
  website: "",
  entityType: "",
  ein: "",
  industry: "",
  businessStartDate: "",
  businessPhone: "",
  businessEmail: "",
  address: {
    line1: "",
    line2: "",
    city: "",
    state: "",
    zip: "",
  },

  // Owner Info
  owners: [
    {
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      ssn: "",
      phone: "",
      email: "",
      address: {
        line1: "",
        line2: "",
        city: "",
        state: "",
        zip: "",
      },
      ownershipPercentage: 100,
    },
  ],

  // Bank Statements
  bankStatements: [],

  currentStep: 0,
};

export const entityTypeOptions = [
  { value: "LLC", label: "LLC" },
  { value: "Corporation", label: "Corporation" },
  { value: "Partnership", label: "Partnership" },
  { value: "Sole Proprietorship", label: "Sole Proprietor" },
  { value: "Nonprofit", label: "Non-Profit" },
];

export const industryOptions = [
  { value: "Agriculture", label: "Agriculture" },
  {
    value: "Arts, Entertainment, & Recreation",
    label: "Arts, Entertainment, & Recreation",
  },
  {
    value: "Automotive, Trucking, & Transportation",
    label: "Automotive, Trucking, & Transportation",
  },
  { value: "Construction & Real Estate", label: "Construction & Real Estate" },
  { value: "Education & Training", label: "Education & Training" },
  {
    value: "Food, Hospitality & Tourism",
    label: "Food, Hospitality & Tourism",
  },
  { value: "Manufacturing & Production", label: "Manufacturing & Production" },
  { value: "Medical & Healthcare", label: "Medical & Healthcare" },
  { value: "Non-profits", label: "Non-profits" },
  {
    value: "Professional Services & Consulting",
    label: "Professional Services & Consulting",
  },
  { value: "Retail & E-commerce", label: "Retail & E-commerce" },
  {
    value: "Tech, Media & Communication",
    label: "Tech, Media & Communication",
  },
  { value: "Other", label: "Other" },
];

export const stateOptions = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" },
  { value: "DC", label: "District of Columbia" },
];

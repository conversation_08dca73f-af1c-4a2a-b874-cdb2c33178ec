import { parsePhoneNumber, parseEIN, parseSS<PERSON> } from "./formatters";

const MIN_FUNDING_AMOUNT = 5_000;
const MAX_FUNDING_AMOUNT = 1_500_000;

export const validationSchema = {
  // Bank Statements
  bankStatements: {
    validate: {
      required: (value) => {
        // Allow either 3 statements or an empty array (for skip option)
        return (
          (value && (value.length === 3 || value.length === 0)) ||
          "Please upload 3 bank statements or skip this step"
        );
      },
    },
  },
  // Funding Information
  fundingAmount: {
    required: "Funding amount is required",
    min: {
      value: MIN_FUNDING_AMOUNT,
      message: `Funding amount must be at least $${MIN_FUNDING_AMOUNT.toLocaleString()}`,
    },
    max: {
      value: MAX_FUNDING_AMOUNT,
      message: `Funding amount is limited to $${MAX_FUNDING_AMOUNT.toLocaleString()}`,
    },
  },
  purpose: {
    required: "Purpose is required",
  },
  topPriority: {
    required: "Top Priority is required",
  },
  timeline: {
    required: "Funding Timeline is required",
  },

  // Business Information
  businessName: {
    required: "Business name is required",
    minLength: {
      value: 2,
      message: "Business name is required",
    },
    maxLength: {
      value: 100,
      message: "Please enter a shorter Business Name",
    },
  },
  monthlyRevenue: {
    required: "Monthly revenue is required",
  },
  annualRevenue: {
    // Optional field, only required when monthlyRevenue is "0-10000"
    min: {
      value: 0,
      message: "Annual revenue must be 0 or greater",
    },
  },
  businessStartDate: {
    required: "Business start date is required",
    validate: {
      notFuture: (value) => {
        if (!value) return true; // Let required handle empty values
        const startDate = new Date(value);
        const currentDate = new Date();
        return (
          startDate <= currentDate ||
          "Please select a valid business start date"
        );
      },
    },
  },
  businessStartConfirmation: {
    // Optional field, only required when business is 6 months or less old
  },

  // Owner Information
  firstName: {
    required: "First name is required",
    minLength: {
      value: 2,
      message: "First name is required",
    },
    maxLength: {
      value: 50,
      message: "Please enter a shorter First Name",
    },
  },
  lastName: {
    required: "Last name is required",
    minLength: {
      value: 2,
      message: "Last name is required",
    },
    maxLength: {
      value: 50,
      message: "Please enter a shorter Last Name",
    },
  },
  email: {
    required: "Email is required",
    pattern: {
      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "Please enter a valid email address",
    },
  },
  phone: {
    required: "Phone number is required",
    validate: {
      validPhone: (value) => {
        const digits = parsePhoneNumber(value);
        return digits.length >= 10 || "Please enter a valid US phone number";
      },
    },
  },
  estimatedFICO: {
    required: "Estimated credit score is required",
  },
  consent: {
    required: "You must accept to proceed",
  },
  dbaName: {
    // Optional
  },
  website: {
    // Optional
    pattern: {
      value:
        /^(https?:\/\/)?(www\.)?[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/.+)?$/,
      message: "Please enter a valid website URL",
    },
  },
  entityType: {
    required: "Entity type is required",
  },
  ein: {
    required: "EIN is required",
    validate: {
      validEIN: (value) => {
        const digits = parseEIN(value);
        return digits.length === 9 || "Please enter a valid EIN";
      },
    },
  },
  industry: {
    required: "Industry is required",
  },
  businessPhone: {
    required: "Business phone is required",
    validate: {
      validPhone: (value) => {
        const digits = parsePhoneNumber(value);
        return digits.length >= 10 || "Please enter a valid US phone number";
      },
    },
  },
  businessEmail: {
    required: "Business email is required",
    pattern: {
      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "Please enter a valid email address",
    },
  },

  // Business Address
  "address.line1": {
    required: "Street address is required",
  },
  "address.line2": {
    // Optional
  },
  "address.city": {
    required: "City is required",
  },
  "address.state": {
    required: "State is required",
  },
  "address.zip": {
    required: "ZIP code is required",
    pattern: {
      value: /^\d{5}$/,
      message: "Please enter a valid ZIP code",
    },
  },

  // Owner Information
  "owners[0].dateOfBirth": {
    required: "Date of birth is required",
    min: {
      value: "1900-01-01",
      message: "Please select a valid date of birth",
    },
    max: {
      value: new Date(new Date().setFullYear(new Date().getFullYear() - 16))
        .toISOString()
        .split("T")[0],
      message: "Please select a valid date of birth",
    },
  },
  "owners[0].ssn": {
    required: "Social Security Number is required",
    validate: {
      validSSN: (value) => {
        const digits = parseSSN(value);
        return digits.length === 9 || "Please enter a valid SSN";
      },
    },
  },
  "owners[0].ownershipPercentage": {
    required: "Ownership percentage is required",
    min: {
      value: 1,
      message: "Ownership percentage must be at least 1%",
    },
    max: {
      value: 100,
      message: "Ownership percentage cannot exceed 100%",
    },
  },
};

import { useEffect, lazy, Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { PreQualifyResult } from "./components/PreQualifyResult/PreQualifyResult.jsx";
import { ApplicationResult } from "./components/ApplicationResult/ApplicationResult.jsx";
import { useQueryParamsLoad } from "./hooks/useQueryParamsLoad.js";
import { Header } from "./components/ui/Header.jsx";
import { PreQualifyPage } from "./pages/PreQualifyPage.jsx";
import { AppFormPage } from "./pages/AppFormPage.jsx";
import { NotFoundPage } from "./pages/NotFoundPage.jsx";
import { FastTrackPage } from "./pages/FastTrackPage.jsx";
import { initializeGA, trackCustomEvent } from "./utils/analytics.js";
import { initializeHotjar } from "./utils/hotjar.js";
import { initializeGTM } from "./utils/gtm.js";
import { IS_DEV_MODE } from "./utils/consts";
import { FooterSkeleton } from "./components/ui/Skeletons.jsx";
import { collectUtmCookies, removeCookieHelper, setUtmCookie } from "./utils/cookieHelpers";
import { useState } from "react";

const Footer = lazy(() => import("./components/ui/Footer.jsx"));

const gaTrackingId = import.meta.env.VITE_GA_TRACKING_ID;
const adwordsId = import.meta.env.VITE_ADWORDS_ID;

initializeGA(gaTrackingId, adwordsId);

initializeHotjar();
initializeGTM();

function trackUtmParameters(utmParams) {
  if (!utmParams || Object.keys(utmParams).length === 0) return;

  // Track each UTM parameter as a separate event
  Object.entries(utmParams).forEach(([key, value]) => {
    if (value) {
      trackCustomEvent(key, value);
    }
  });
}

// Helper to check debug mode (same as logger.js)
function isDebugMode() {
  if (import.meta.env.VITE_DEBUG_MODE === "true") return true;
  if (typeof window === "undefined") return false;
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("debug") === "true";
}

function DebugUtmBox() {
  const [utmCookies, setUtmCookies] = useState({});
  const [closed, setClosed] = useState(false);

  useEffect(() => {
    if (closed) return;
    const update = () => setUtmCookies(collectUtmCookies());
    update();
    const interval = setInterval(update, 2000);
    return () => clearInterval(interval);
  }, [closed]);

  const handleClose = () => {
    setClosed(true);
    sessionStorage.setItem('utmBoxClosed', 'true');
  };

  if (closed || !Object.keys(utmCookies).length) return null;

  const handleClearAll = () => {
    Object.keys(utmCookies).forEach((name) => removeCookieHelper(name));
    setUtmCookies({});
  };

  return (
    <div style={{
      position: "fixed",
      bottom: 16,
      right: 16,
      background: "rgba(0,0,0,0.75)",
      color: "#fff",
      padding: "12px 16px",
      borderRadius: 8,
      fontSize: 13,
      zIndex: 10000,
      maxWidth: "92%",
      boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
      fontFamily: 'monospace',
    }}>
      <button
        onClick={handleClose}
        style={{
          position: 'absolute',
          top: 4,
          right: 8,
          background: 'none',
          border: 'none',
          color: '#aaa',
          fontSize: 14,
          cursor: 'pointer',
        }}
        aria-label="Close debug UTM box"
        title="Close"
      >
        ×
      </button>
      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>UTM Cookies</div>
      <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
        {Object.entries(utmCookies)
          .map(([k, v]) => `${k}: ${v}`)
          .join('\n')}
      </pre>
      <button onClick={handleClearAll} style={{ marginTop: 8, background: "#fff", color: "rgba(0,0,0,0.75)", border: 'none', borderRadius: 4, padding: '2px 6px' }}>Clear All</button>
    </div>
  );
}


/**
 * Main application component
 * @returns {JSX.Element}
 */
function App() {
  const { utmParams } = useQueryParamsLoad();

  if (IS_DEV_MODE) {
    console.log("Dev Mode!");
    console.table(import.meta.env);
  }

  useEffect(() => {
    if (document.referrer) {
      setUtmCookie("utm_referrer", document.referrer);
    }
  }, []);

  // Track UTM parameters when the app loads
  useEffect(() => {
    if (utmParams && Object.keys(utmParams).length > 0) {
      trackUtmParameters(utmParams);
    }
  }, [utmParams]);

  return (
    <Router>
      <div className="min-h-screen font-lexend bg-gray-100">
        <Header />

        {IS_DEV_MODE && (
          <div style={{
            position: "fixed",
            bottom: 16,
            left: 16,
            background: "rgba(255,0,0,0.75)",
            color: "#fff",
            padding: "12px 16px",
            borderRadius: 8,
            fontSize: 20,
            zIndex: 999999,
            maxWidth: 320,
          }}>
            <div style={{ fontWeight: 'bold' }}>Dev Mode!</div>
          </div>
        )}

        {isDebugMode() && <DebugUtmBox />}

        <main className="grid py-10 min-h-[calc(100vh-64px-300px)] sm:min-h-[calc(100vh-80px-348px)] lg:min-h-[calc(100vh-96px-372px)]">
          <div className="self-center">
            <Routes>
              <Route path="/" element={<PreQualifyPage />} />
              <Route path="/line-of-credit" element={<PreQualifyPage />} />
              <Route path="/ft" element={<FastTrackPage />} />
              <Route
                path="/prequalify-result/:uuid"
                element={<PreQualifyResult />}
              />
              <Route path="/application/:uuid" element={<AppFormPage />} />
              <Route
                path="/application/:uuid/result"
                element={<ApplicationResult />}
              />
              {/* Catch-all route for 404 page */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </div>
        </main>
        <Suspense fallback={<FooterSkeleton />}>
          <Footer />
        </Suspense>
      </div>
    </Router>
  );
}

export default App;

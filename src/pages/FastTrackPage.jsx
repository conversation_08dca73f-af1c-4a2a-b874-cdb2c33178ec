import { useRef, useCallback, lazy, Suspense } from "react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FastTrackForm } from "../components/FastTrackForm/FastTrackForm";
import { useValidateRepApi } from "../hooks/useValidateRepApi";
import { useAppStorage } from "../hooks/useAppStorage";
import { trackCustomEvent } from "../utils/analytics";
import {
  isOnFastTrackDomain,
  redirectToFastTrack,
  redirectToMainPortal,
} from "../utils/domainUtils";
import { IS_DEV_MODE } from "../utils/consts";
import { getUtmCookie } from "../utils/cookieHelpers";
import {
  PreQualifyExplainerSkeleton,
  QuickLinksSectionSkeleton,
} from "../components/ui/Skeletons";

const PreQualifyExplainer = lazy(() =>
  import("../components/ui/PreQualifyExplainer")
);

const QuickLinksSection = lazy(() =>
  import("../components/ui/QuickLinksSection")
);

/**
 * Fast Track Page Component
 * Validates utm_rep parameter and shows fast track form if valid
 * @returns {JSX.Element}
 */
export const FastTrackPage = () => {
  const navigate = useNavigate();
  const utmRep = getUtmCookie("utm_rep");
  const { fastTrackFormParams, fastTrackActive, setFastTrackActive } =
    useAppStorage();
  const { validateRep } = useValidateRepApi();
  const formInitialized = useRef(false);
  const [isValidRep, setIsValidRep] = useState(false);

  const handleDomainRedirect = useCallback(() => {
    setIsValidRep(false);
    setFastTrackActive(false);

    if (IS_DEV_MODE) {
      navigate("/", { replace: true });
      return;
    }

    if (isOnFastTrackDomain()) {
      redirectToMainPortal();
    } else {
      navigate("/", { replace: true });
    }
  }, [setFastTrackActive, navigate]);

  useEffect(() => {
    if (formInitialized.current) return;

    const checkRepValidation = async () => {
      // Check if utm_rep parameter exists

      if (!utmRep) {
        // No utm_rep parameter, redirect to prequalify page
        handleDomainRedirect();
        return;
      }

      try {
        // Validate the rep parameter
        const validationResult = await validateRep(utmRep);

        if (!validationResult?.valid) {
          // Invalid rep, redirect to prequalify page
          handleDomainRedirect();
          return;
        } else {
          if (!formInitialized.current) {
            trackCustomEvent("fast_track", true);
            setIsValidRep(true);
            formInitialized.current = true;
            if (!fastTrackActive) setFastTrackActive(true);

            if (!isOnFastTrackDomain()) {
              redirectToFastTrack();
            }
          }
        }
      } catch (error) {
        // Error validating rep, redirect to prequalify page
        console.error("Error validating rep:", error);
        handleDomainRedirect();
      }
    };

    checkRepValidation();
  }, [
    utmRep,
    validateRep,
    navigate,
    fastTrackActive,
    setFastTrackActive,
    handleDomainRedirect,
  ]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
        <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
          <Suspense fallback={<PreQualifyExplainerSkeleton />}>
            <PreQualifyExplainer />
          </Suspense>
        </div>

        <div className="w-full lg:w-2/3">
          <FastTrackForm
            fastTrackParams={fastTrackFormParams}
            isValidRep={isValidRep}
          />
        </div>
      </div>
      <Suspense fallback={<QuickLinksSectionSkeleton />}>
        <QuickLinksSection />
      </Suspense>
    </div>
  );
};
